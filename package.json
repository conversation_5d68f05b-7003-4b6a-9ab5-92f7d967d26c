{"name": "nahed-bashtah", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "clean-cache": "node scripts/cleanCache.js", "build": "npm run clean-cache && prisma generate && prisma migrate deploy && next build && npm run copy", "copy": "node scripts/copyFiles.js", "cleanup": "rm -r node_modules", "start": "next start", "lint": "next lint"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.5.2", "@atlaskit/pragmatic-drag-and-drop-flourish": "^2.0.2", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.0.3", "@atlaskit/pragmatic-drag-and-drop-live-region": "^1.3.0", "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator": "^3.1.0", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@prisma/client": "^6.6.0", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-highlight": "^2.13.0", "@tiptap/extension-text-align": "^2.13.0", "@tiptap/pm": "^2.13.0", "@tiptap/react": "^2.13.0", "@tiptap/starter-kit": "^2.13.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "filepond": "^4.32.7", "filepond-plugin-file-validate-type": "^1.2.9", "fuse.js": "^7.1.0", "jose": "^6.0.10", "lucide-react": "^0.488.0", "next": "^15.3.0", "next-themes": "^0.4.6", "nodemailer": "^7.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-filepond": "^7.1.3", "resend": "^4.5.1", "server-only": "^0.0.1", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tiny-invariant": "^1.3.3", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.8.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.6.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}