import prisma from "@/lib/prisma";
import PageLayout from "@/components/dashboard-workspace/PageLayout";

import { dataLimits } from "@/utils/siteConfig";
import { revalidatePath } from "next/cache";
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";
import BlogPostForm from "@/components/dashboard-workspace/blogPosts-CRUD/BlogPostsForm";
import { blogPostSchema } from "@/components/dashboard-workspace/blogPosts-CRUD/blogPostsSchema";

export default function CreateBlogPostPage() {
  return (
    <PageLayout title="إضافة منشور جديد">
      <BlogPostForm
        mode="create"
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = blogPostSchema.omit({ id: true }).safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const image = await handleFileUpdate("", data.image, "blog/image/");

          const [totalCount] = await prisma.$transaction([
            prisma.blogPost.count(),
            prisma.blogPost.create({ data: { ...data, image } }),
          ]);

          const totalPages = Math.ceil(totalCount / dataLimits.blogPost);

          revalidatePath("/admin/blogPosts");
          for (let page = 1; page <= totalPages; page++) revalidatePath(`/blog/${page}`);

          return { success: true, message: "تم اضافة المنشور بنجاح" };
        }}
      />
    </PageLayout>
  );
}
