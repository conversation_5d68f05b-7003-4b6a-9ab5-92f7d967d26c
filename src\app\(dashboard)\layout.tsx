import { Metadata } from "next";
import "./globals-dashboard.css";

// هذا التخطيط يلتف حول صفحات لوحة التحكم بالكامل بالإضافة الى صفحة تسجيل الدخول الخاصة بلوحة التحكم
// قم بإستيراد الحزم المشتركة هنا, على سبيل المثال (الخطوط, ملف سي اس اس)

import { Tajawal } from "next/font/google";
import { siteName } from "@/utils/siteConfig";

const fontTajawal = Tajawal({
  weight: ["200", "300", "400", "500", "700", "800", "900"],
  subsets: ["arabic"],
  display: "swap",
});

export const metadata: Metadata = {
  title: `لوحة التحكم | ${siteName}`,
  robots: {
    index: false,
    follow: false,
  },
};

type Props = { children: React.ReactNode };
export default function DashboardMainLayout({ children }: Props) {
  return <main className={`h-full w-full text-gray-800 dark:text-gray-200 ${fontTajawal.className}`}>{children}</main>;
}
