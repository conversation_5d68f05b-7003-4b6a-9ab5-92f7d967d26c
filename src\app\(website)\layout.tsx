import Footer from "@/components/ui/footer/footer";
import Nav from "@/components/ui/nav/nav";
import { Toaster } from "@/components/ui/sonner";
import { Noto_Kufi_Arabic } from "next/font/google";
import "@/app/globals.css";
import { SessionContextProvidar } from "@/components/context/user-context";
import type { Metadata } from "next";
import { siteName } from "@/utils/siteConfig";

const fontNotoKufi = Noto_Kufi_Arabic({
  weight: "variable",
  subsets: ["arabic"],
  display: "swap",
});

export const metadata: Metadata = {
  title: siteName,
  generator: "Next.js",
  creator: "ناهد باشطح",
  applicationName: siteName,
  authors: [{ name: "ناهد باشطح" }],
  publisher: "اكاديمة الدكتورة ناهد باشطح",
  metadataBase: new URL(String(process.env.DOMAIN)),
  description:
    "اكاديمية د. ناهد باشطح لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات. تقنيات فعّالة لتحقيق التوازن النفسي والجسدي",
  keywords: [
    "كورسات د. ناهد باشطح",
    "التحرر من المشاعر المكبوتة",
    "التوازن النفسي",
    "التعافي من الصدمات",
    "تحسين القرارات",
    "التغلب على القلق",
    "الصحة النفسية",
    "تقنيات التوازن النفسي",
    "القلق",
    "الصدمات",
    "المشاعر",
    "التوتر",
    "كورس",
    "كورسات",
    "ناهد",
    "باشطح",
    "الدكتورة",
    "النفس",
    "التوازن",
    "القرارات",
    "التحرر",
    "دورات الصحة النفسية",
    "التخلص من المشاعر السلبية",
    "إدارة القلق والتوتر",
    "تقنيات التعافي النفسي",
    "دورات تطوير الذات",
    "العلاج النفسي الذاتي",
    "تحسين الصحة العقلية",
    "دورات التوازن النفسي والجسدي",
    "التخلص من الصدمات النفسية",
    "تقنيات الاسترخاء والتفكير الإيجابي",
    "دورات العلاج النفسي",
    "تحسين جودة الحياة النفسية",
    "التفكير الواعي واتخاذ القرارات",
    "دورات التعامل مع الضغوط النفسية",
    "التحرر من الأفكار السلبية",
    "تقنيات العلاج المعرفي السلوكي",
    "دورات الصحة العاطفية",
    "تحقيق السلام الداخلي",
    "دورات التخلص من القلق المزمن",
  ],
};

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <main className={`${fontNotoKufi.className} mx-auto max-w-(--breakpoint-2xl)`}>
      <SessionContextProvidar>
        <Nav />
        {children}
        <Footer />
        <Toaster richColors position="top-center" />
      </SessionContextProvidar>
    </main>
  );
}
