import AdminTable from "@/components/dashboard-workspace/admins-CRUD/AdminTable";
import { deleteFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles";
import prisma from "@/lib/prisma";

async function getAllAdmins() {
  return await prisma.admin.findMany();
}

export default async function UsersPage() {
  const admins = await getAllAdmins();
  return (
    <AdminTable
      data={admins}
      onDelete={async ({ data }) => {
        "use server";

        const dataLength = data.length;
        const adminsLength = await prisma.admin.count();

        if (dataLength >= adminsLength) {
          return {
            success: false,
            message: "لا يمكن حذف جميع المشرفين, يجب ان يبقى مشرف واحد على الأقل",
          };
        }

        await prisma.admin.deleteMany({
          where: { id: { in: data.map((admin) => admin.id) } },
        });

        const deleteFile = (sourse: string | null) =>
          sourse ? deleteFileInR2({ fileUrl: sourse }) : Promise.resolve();

        await Promise.all(data.map((admin) => deleteFile(admin.avatarUrl)));

        return { success: true, message: "تم حذف المشرف بنجاح" };
      }}
    />
  );
}
