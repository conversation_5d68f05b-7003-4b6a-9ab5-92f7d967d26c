import { courseSchema } from "@/components/dashboard-workspace/courses-CRUD/courseSchema";
import { moveFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles";
import CourseForm from "@/components/dashboard-workspace/courses-CRUD/CourseForm";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";

export default function CreateCourse() {
  return (
    <PageLayout title="إضافة كورس جديد" description="">
      <CourseForm
        mode="create"
        defaultValues={{ modulesCount: 0, previewInHomePage: false }}
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = courseSchema.safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const newPosterUrl = await moveFileInR2({
            fileUrl: data.posterUrl,
            moveFrom: "tmp/",
            moveTo: "courses/image/",
          });

          await prisma.course.create({ data: { ...data, posterUrl: newPosterUrl } });

          revalidatePath(`/courses`);
          revalidatePath(`/admin/courses`);
          if (data.previewInHomePage) revalidatePath(`/`);

          return { success: true, message: "تم إضافة الكورس بنجاح" };
        }}
      />
    </PageLayout>
  );
}
