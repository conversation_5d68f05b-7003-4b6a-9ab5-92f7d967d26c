import InterviewsTable from "@/components/dashboard-workspace/interviews-CRUD/InterviewsTable";
import { deleteFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";

export default async function InterviewsPage() {
  const interviews = await prisma.interview.findMany({ orderBy: { createdAt: "desc" } });

  return (
    <InterviewsTable
      data={interviews}
      onDelete={async ({ data }) => {
        "use server";

        await prisma.interview.deleteMany({
          where: { id: { in: data.map((interview) => interview.id) } },
        });

        await Promise.all(data.map((interview) => deleteFileInR2({ fileUrl: interview.thumbnail })));

        revalidatePath("/admin/interviews");
        revalidatePath("/(website)/media-experience/interviews", "layout");

        return { success: true, message: "تم الحذف بنجاح" };
      }}
    />
  );
}
