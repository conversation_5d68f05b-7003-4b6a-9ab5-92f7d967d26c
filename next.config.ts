import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: "standalone",

  cacheMaxMemorySize: 1000000000, // 1 GB

  // eslint: {
  //   ignoreDuringBuilds: true,
  // },
  // typescript: {
  //   ignoreBuildErrors: true,
  // },


  async headers() {
    return [
      {
        source: "/:path*{/}?",
        headers: [
          {
            key: "X-Accel-Buffering",
            value: "no",
          },
        ],
      },
    ];
  },

  images: {
    formats: ["image/webp"],
    deviceSizes: [640, 768, 1024, 1280, 1536],

    remotePatterns: [
      {
        protocol: "https",
        hostname: "pub-431af6a83a2349a29a6c160751fe1f9e.r2.dev",
        port: "",
        pathname: "/**",
        search: "",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
        pathname: "/**",
        search: "*",
      },
    ],
  },
};

export default nextConfig;
