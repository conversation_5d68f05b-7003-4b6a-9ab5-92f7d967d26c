import BlogPostsTable from "@/components/dashboard-workspace/blogPosts-CRUD/BlogPostsTable";
import prisma from "@/lib/prisma";

import { deleteFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles";
import { revalidatePath } from "next/cache";

export default async function BlogPostsPage() {
  const blogPosts = await prisma.blogPost.findMany({ orderBy: { createdAt: "desc" } });

  return (
    <BlogPostsTable
      data={blogPosts}
      onDelete={async ({ data }) => {
        "use server";

        await prisma.blogPost.deleteMany({
          where: { id: { in: data.map((blogPost) => blogPost.id) } },
        });

        await Promise.all(data.map((blogPost) => deleteFileInR2({ fileUrl: blogPost.image })));

        revalidatePath("/admin/blogPosts");
        revalidatePath("/(website)/blog", "layout");

        return { success: true, message: "تم الحذف بنجاح" };
      }}
    />
  );
}
