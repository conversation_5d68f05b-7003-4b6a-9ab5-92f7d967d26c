"use client";
import { DataTablePropsDef } from "./types";
import { DataTableTremor } from "./DataTableTremor";
import { generateColumns } from "./generateColumns";
import { generateActions } from "./generateActions";
import { generateFilters } from "./generateFilters";

type Props<TData extends { [K: string]: any }> = DataTablePropsDef<TData>;

export default function DataTable<TData extends { [K: string]: any }>(props: Props<TData>) {
  const {
    rowActions,
    data,
    primaryKey,
    columnSearch,
    createColumns,
    defaultPageSize,
    createDataButton,
    enableRowSelection,
  } = props;

  const actions = generateActions({ createColumns, rowActions });
  const columns = generateColumns({ createColumns, actions });
  const filters = generateFilters({ createColumns });

  return (
    <div className="max-w-full">
      <DataTableTremor
        columns={columns}
        data={data}
        filters={filters}
        primaryKey={primaryKey}
        columnSearch={columnSearch}
        defaultPageSize={defaultPageSize}
        createDataButton={createDataButton}
        enableRowSelection={enableRowSelection}
        onDelete={actions.onDelete}
      />
    </div>
  );
}
