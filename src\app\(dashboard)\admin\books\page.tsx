import BooksTable from "@/components/dashboard-workspace/books-CRUD/BooksTable";
import { deleteFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";

export default async function page() {
  const books = await prisma.book.findMany({ orderBy: { createdAt: "desc" } });

  return (
    <BooksTable
      data={books}
      onDelete={async ({ data }) => {
        "use server";

        await prisma.book.deleteMany({
          where: { id: { in: data.map((book) => book.id) } },
        });

        await Promise.all(
          data.flatMap((book) => [
            deleteFileInR2({ fileUrl: book.coverImage }),
            deleteFileInR2({ fileUrl: book.bookUrl }),
          ]),
        );

        revalidatePath("/admin/books");
        revalidatePath("/(website)/media-experience/books", "layout");

        return { success: true, message: "تم الحذف بنجاح" };
      }}
    />
  );
}
