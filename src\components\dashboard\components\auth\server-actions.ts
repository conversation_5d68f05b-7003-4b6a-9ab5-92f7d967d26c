"use server";

import { redirect } from "next/navigation";
import { createAdminSession, deleteAdminSession, verifyAdminSession } from "./admin-session";
import prisma from "@/lib/prisma";

export async function getAdminSession() {
  const adminSession = await verifyAdminSession();

  const admin = await prisma.admin.findUnique({ where: { id: adminSession?.id } });

  if (!admin || admin?.status === "disabled") {
    await deleteAdminSession();
    redirect("/dashboard-login");
  }

  await createAdminSession({
    id: admin.id,
    name: admin.name,
    email: admin.email,
    status: admin.status,
    accessiblePages: admin.accessiblePages,
    avatarUrl: admin.avatarUrl,
    role: admin.role,
  });

  return adminSession;
}

export async function logOutAction() {
  await deleteAdminSession();
}
