import TestimonysTable from "@/components/dashboard-workspace/testimonial-CRUD/TestimonyTable";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";


export default async function TestimonysPage() {
  const testimonys = await prisma.testimony.findMany();

  return (
    <TestimonysTable
      data={testimonys}
      onDelete={async ({ data }) => {
        "use server";

        await prisma.testimony.deleteMany({
          where: { id: { in: data.map((testimony) => testimony.id) } },
        });

        revalidatePath("/admin/testimonys");
        revalidatePath("/");

        return { success: true, message: "تم الحذف بنجاح" };
      }}
    />
  );
}
