import DashboardLayout from "@/components/dashboard/components/layout/DashboardLayout";
import { House, LayoutTemplate, PackageSearch, UserCog } from "lucide-react";
import { Navigation2Type, NavigationType } from "@/components/dashboard/components/layout/LayoutTypes";
import { LogoDashboard } from "@/components/dashboard-workspace/logo-dashboard";
import { getConsultationUnReadCountCache } from "@/components/dashboard-workspace/consultations-CRUD/db-queries";

export default async function AdminLayout({ children }: { children: React.ReactNode }) {
  const consultationUnReadCount = await getConsultationUnReadCountCache();

  const navigation: NavigationType[] = [
    {
      label: "الرئيسية",
      href: "/admin",
      icon: <House />,
      notifications: false,
    },
    {
      label: "البريد",
      href: "/admin/inbox",
      icon: <PackageSearch />,
      notifications: consultationUnReadCount <= 0 ? false : consultationUnReadCount,
    },
  ];

  const navigation2: Navigation2Type[] = [
    {
      id: "1",
      label: "إدارة المحتوى",
      icon: <LayoutTemplate />,
      children: [
        { label: "الكورسات", href: "/admin/courses" },
        { label: "المقالات", href: "/admin/articles" },
        { label: "الكتب", href: "/admin/books" },
        { label: "المقابلات", href: "/admin/interviews" },
        { label: "منشورات المدونة", href: "/admin/blogPosts" },
        { label: "شهادات العملاء", href: "/admin/testimonys" },
        { label: "إعدادات الموقع", href: "/admin/site-settings" },
        { label: "اهداء كورس", href: "/admin/user-authorization" },
      ],
    },
    {
      id: "2",
      label: "إدارة المشرفين",
      icon: <UserCog />,
      children: [{ label: "المشرفين", href: "/admin/admins-management" }],
    },
  ];

  return (
    <DashboardLayout logo={<LogoDashboard />} sidebarItems={{ navigation, navigation2 }}>
      {children}
    </DashboardLayout>
  );
}
