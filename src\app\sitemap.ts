import { MetadataRoute } from "next"

const getUrl = (segment: `/${string}`) => {
  return `${process.env.DOMAIN}${segment}`
}

//  القيم المقترحة:
//  - `1`: للصفحة الرئيسية
//  - `0.8`: للصفحات الرئيسية (المنتجات، المدونة، الخدمات)
//  - `0.6`: لصفحات المنتجات الفردية أو مقالات المدونة
//  - `0.4`: للصفحات الثانوية
//  - `0.2`: للصفحات الأقل أهمية

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  return [
    {
      url: process.env.DOMAIN ?? "",
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 1,
    },
    {
      url: getUrl("/courses"),
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: getUrl("/blog"),
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: getUrl("/consultation"),
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.6,
    },
    {
      url: getUrl("/about-me"),
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.7,
    },
    {
      url: getUrl("/auth/login"),
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.4,
    },
    {
      url: getUrl("/auth/signup"),
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.4,
    },
  ]
}
